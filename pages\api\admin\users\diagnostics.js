import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * User Management Diagnostics API
 * Analyzes data consistency across user-related tables
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only administrators and developers can access diagnostics.' 
      })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    const { email } = req.query
    const diagnostics = {
      timestamp: new Date().toISOString(),
      email_filter: email || null,
      tables: {},
      inconsistencies: [],
      recommendations: []
    }

    // 1. Check auth.users table
    console.log('Checking auth.users table...')
    try {
      let authUsers = []
      if (email) {
        // Check specific email
        const { data: authData, error: authError } = await adminClient.auth.admin.listUsers()
        if (authError) throw authError
        authUsers = authData.users.filter(u => u.email === email)
      } else {
        // Get all users (limited for performance)
        const { data: authData, error: authError } = await adminClient.auth.admin.listUsers({
          page: 1,
          perPage: 1000
        })
        if (authError) throw authError
        authUsers = authData.users
      }

      diagnostics.tables.auth_users = {
        count: authUsers.length,
        users: authUsers.map(u => ({
          id: u.id,
          email: u.email,
          created_at: u.created_at,
          email_confirmed_at: u.email_confirmed_at
        }))
      }
    } catch (error) {
      diagnostics.tables.auth_users = { error: error.message }
    }

    // 2. Check user_roles table
    console.log('Checking user_roles table...')
    try {
      let rolesQuery = adminClient.from('user_roles').select('*')
      if (email && diagnostics.tables.auth_users?.users) {
        const userIds = diagnostics.tables.auth_users.users.map(u => u.id)
        if (userIds.length > 0) {
          rolesQuery = rolesQuery.in('id', userIds)
        }
      }
      
      const { data: rolesData, error: rolesError } = await rolesQuery
      if (rolesError) throw rolesError

      diagnostics.tables.user_roles = {
        count: rolesData.length,
        users: rolesData
      }
    } catch (error) {
      diagnostics.tables.user_roles = { error: error.message }
    }

    // 3. Check user_profiles table
    console.log('Checking user_profiles table...')
    try {
      let profilesQuery = adminClient.from('user_profiles').select('*')
      if (email && diagnostics.tables.auth_users?.users) {
        const userIds = diagnostics.tables.auth_users.users.map(u => u.id)
        if (userIds.length > 0) {
          profilesQuery = profilesQuery.in('id', userIds)
        }
      }
      
      const { data: profilesData, error: profilesError } = await profilesQuery
      if (profilesError) throw profilesError

      diagnostics.tables.user_profiles = {
        count: profilesData.length,
        users: profilesData
      }
    } catch (error) {
      diagnostics.tables.user_profiles = { error: error.message }
    }

    // 4. Check artist_braider_applications table
    console.log('Checking artist_braider_applications table...')
    try {
      let appsQuery = adminClient.from('artist_braider_applications').select('*')
      if (email && diagnostics.tables.auth_users?.users) {
        const userIds = diagnostics.tables.auth_users.users.map(u => u.id)
        if (userIds.length > 0) {
          appsQuery = appsQuery.in('user_id', userIds)
        }
      }
      
      const { data: appsData, error: appsError } = await appsQuery
      if (appsError) throw appsError

      diagnostics.tables.artist_braider_applications = {
        count: appsData.length,
        users: appsData
      }
    } catch (error) {
      diagnostics.tables.artist_braider_applications = { error: error.message }
    }

    // 5. Check user_activity_log table
    console.log('Checking user_activity_log table...')
    try {
      let activityQuery = adminClient.from('user_activity_log').select('user_id, activity_type, created_at').order('created_at', { ascending: false }).limit(100)
      if (email && diagnostics.tables.auth_users?.users) {
        const userIds = diagnostics.tables.auth_users.users.map(u => u.id)
        if (userIds.length > 0) {
          activityQuery = activityQuery.in('user_id', userIds)
        }
      }
      
      const { data: activityData, error: activityError } = await activityQuery
      if (activityError) throw activityError

      diagnostics.tables.user_activity_log = {
        count: activityData.length,
        recent_activities: activityData
      }
    } catch (error) {
      diagnostics.tables.user_activity_log = { error: error.message }
    }

    // 6. Analyze inconsistencies
    if (diagnostics.tables.auth_users?.users && diagnostics.tables.user_roles?.users && diagnostics.tables.user_profiles?.users) {
      const authUserIds = new Set(diagnostics.tables.auth_users.users.map(u => u.id))
      const roleUserIds = new Set(diagnostics.tables.user_roles.users.map(u => u.id))
      const profileUserIds = new Set(diagnostics.tables.user_profiles.users.map(u => u.id))

      // Find orphaned records
      const authOnlyUsers = [...authUserIds].filter(id => !roleUserIds.has(id) || !profileUserIds.has(id))
      const roleOnlyUsers = [...roleUserIds].filter(id => !authUserIds.has(id))
      const profileOnlyUsers = [...profileUserIds].filter(id => !authUserIds.has(id))

      if (authOnlyUsers.length > 0) {
        diagnostics.inconsistencies.push({
          type: 'orphaned_auth_users',
          description: 'Users exist in auth.users but missing from user_roles or user_profiles',
          user_ids: authOnlyUsers,
          count: authOnlyUsers.length
        })
      }

      if (roleOnlyUsers.length > 0) {
        diagnostics.inconsistencies.push({
          type: 'orphaned_user_roles',
          description: 'Roles exist without corresponding auth.users records',
          user_ids: roleOnlyUsers,
          count: roleOnlyUsers.length
        })
      }

      if (profileOnlyUsers.length > 0) {
        diagnostics.inconsistencies.push({
          type: 'orphaned_user_profiles',
          description: 'Profiles exist without corresponding auth.users records',
          user_ids: profileOnlyUsers,
          count: profileOnlyUsers.length
        })
      }
    }

    // 7. Generate recommendations
    if (diagnostics.inconsistencies.length > 0) {
      diagnostics.recommendations.push('Run cleanup procedures to fix orphaned records')
      diagnostics.recommendations.push('Update deletion logic to check auth.users table instead of user_profiles')
      diagnostics.recommendations.push('Ensure user creation creates all required related records')
    } else {
      diagnostics.recommendations.push('No inconsistencies detected - user data is synchronized')
    }

    return res.status(200).json(diagnostics)

  } catch (error) {
    console.error('Error in user diagnostics:', error)
    return res.status(500).json({ 
      error: 'Diagnostics failed', 
      message: error.message 
    })
  }
}
