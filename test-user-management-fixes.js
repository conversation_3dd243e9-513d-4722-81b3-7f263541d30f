/**
 * Test script for user management data consistency fixes
 * Run this with: node test-user-management-fixes.js
 * 
 * This script tests the fixes for the user management data inconsistency issue
 */

const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

// Test emails that were causing issues
const TEST_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
];

// Mock authentication token (replace with actual admin token for testing)
const AUTH_TOKEN = 'your-admin-auth-token-here';

async function makeAuthenticatedRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    ...options.headers
  };

  try {
    const response = await fetch(url, {
      ...options,
      headers
    });

    const data = await response.json();
    return { response, data };
  } catch (error) {
    console.error(`Request failed for ${endpoint}:`, error);
    return { response: null, data: { error: error.message } };
  }
}

async function testUserDiagnostics() {
  console.log('\n🔍 Testing User Diagnostics API...');
  
  const { response, data } = await makeAuthenticatedRequest('/api/admin/users/diagnostics');
  
  if (response?.ok) {
    console.log('✅ Diagnostics API working');
    console.log(`📊 Found ${data.inconsistencies?.length || 0} inconsistencies`);
    
    if (data.inconsistencies?.length > 0) {
      console.log('⚠️  Inconsistencies found:');
      data.inconsistencies.forEach(inc => {
        console.log(`   - ${inc.type}: ${inc.description} (${inc.count} records)`);
      });
    }
  } else {
    console.log('❌ Diagnostics API failed:', data.error);
  }
  
  return data;
}

async function testFindUserByEmail(email) {
  console.log(`\n🔍 Testing Find User by Email for: ${email}`);
  
  const { response, data } = await makeAuthenticatedRequest(`/api/admin/users/find-by-email?email=${encodeURIComponent(email)}`);
  
  if (response?.ok) {
    console.log(`✅ Find user API working for ${email}`);
    console.log(`   Found in auth.users: ${data.found ? 'Yes' : 'No'}`);
    
    if (data.found) {
      console.log(`   User ID: ${data.auth_user?.id}`);
      console.log(`   Has role: ${data.user_role ? 'Yes' : 'No'}`);
      console.log(`   Has profile: ${data.user_profile ? 'Yes' : 'No'}`);
      console.log(`   Inconsistencies: ${data.inconsistencies?.length || 0}`);
      
      if (data.inconsistencies?.length > 0) {
        data.inconsistencies.forEach(inc => {
          console.log(`     - ${inc}`);
        });
      }
    }
  } else {
    console.log(`❌ Find user API failed for ${email}:`, data.error);
  }
  
  return data;
}

async function testUserDeletion(email) {
  console.log(`\n🗑️  Testing User Deletion for: ${email}`);
  
  // First find the user to get their ID
  const findResult = await testFindUserByEmail(email);
  
  if (!findResult.found) {
    console.log(`   ℹ️  User ${email} not found - cannot test deletion`);
    return { success: false, reason: 'user_not_found' };
  }
  
  const userId = findResult.auth_user.id;
  console.log(`   Attempting to delete user ID: ${userId}`);
  
  const { response, data } = await makeAuthenticatedRequest('/api/admin/users/delete', {
    method: 'DELETE',
    body: JSON.stringify({ userId })
  });
  
  if (response?.ok) {
    console.log(`✅ User deletion successful for ${email}`);
    console.log(`   Message: ${data.message}`);
    return { success: true, data };
  } else {
    console.log(`❌ User deletion failed for ${email}:`, data.error);
    return { success: false, error: data.error, data };
  }
}

async function testUserCreation(email) {
  console.log(`\n➕ Testing User Creation for: ${email}`);
  
  const testUser = {
    email,
    password: 'TestPassword123!',
    role: 'user',
    name: `Test User ${email}`,
    sendWelcomeEmail: false
  };
  
  const { response, data } = await makeAuthenticatedRequest('/api/admin/users/create', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });
  
  if (response?.ok) {
    console.log(`✅ User creation successful for ${email}`);
    console.log(`   User ID: ${data.user?.id}`);
    console.log(`   Welcome email sent: ${data.welcomeEmail?.sent || false}`);
    return { success: true, data };
  } else {
    console.log(`❌ User creation failed for ${email}:`, data.error);
    return { success: false, error: data.error, data };
  }
}

async function testCleanupOperations() {
  console.log('\n🧹 Testing Cleanup Operations...');
  
  const operations = [
    'create_missing_profiles',
    'create_missing_roles'
  ];
  
  for (const operation of operations) {
    console.log(`\n   Testing cleanup: ${operation}`);
    
    const { response, data } = await makeAuthenticatedRequest('/api/admin/users/cleanup', {
      method: 'POST',
      body: JSON.stringify({ action: operation })
    });
    
    if (response?.ok) {
      console.log(`   ✅ Cleanup ${operation} successful`);
      console.log(`      Created: ${data.results?.created || 0}`);
      console.log(`      Deleted: ${data.results?.deleted || 0}`);
      console.log(`      Errors: ${data.results?.errors?.length || 0}`);
    } else {
      console.log(`   ❌ Cleanup ${operation} failed:`, data.error);
    }
  }
}

async function runFullTest() {
  console.log('🚀 Starting User Management Data Consistency Tests');
  console.log('================================================');
  
  // 1. Run initial diagnostics
  const initialDiagnostics = await testUserDiagnostics();
  
  // 2. Test find user for problematic emails
  const userFindings = {};
  for (const email of TEST_EMAILS) {
    userFindings[email] = await testFindUserByEmail(email);
  }
  
  // 3. Run cleanup operations if needed
  if (initialDiagnostics.inconsistencies?.length > 0) {
    await testCleanupOperations();
  }
  
  // 4. Test the complete user lifecycle for each problematic email
  for (const email of TEST_EMAILS) {
    console.log(`\n🔄 Testing complete lifecycle for: ${email}`);
    
    // Try deletion first (should work now with fixed logic)
    const deleteResult = await testUserDeletion(email);
    
    // Try creation (should work if deletion succeeded)
    if (deleteResult.success || deleteResult.reason === 'user_not_found') {
      const createResult = await testUserCreation(email);
      
      if (createResult.success) {
        // Try deletion again to clean up
        await testUserDeletion(email);
      }
    }
  }
  
  // 5. Run final diagnostics
  console.log('\n📊 Final Diagnostics Check...');
  await testUserDiagnostics();
  
  console.log('\n✅ User Management Data Consistency Tests Complete');
  console.log('================================================');
}

// Run the tests
if (require.main === module) {
  runFullTest().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testUserDiagnostics,
  testFindUserByEmail,
  testUserDeletion,
  testUserCreation,
  testCleanupOperations,
  runFullTest
};
